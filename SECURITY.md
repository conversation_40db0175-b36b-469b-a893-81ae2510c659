# Security Policy

## Supported Versions

The following versions of Pterodactyl are receiving active support and maintenance. Any security vulnerabilities discovered must be reproducible in supported versions.

| Panel  | Daemon       | Supported          |
|--------|--------------|--------------------|
| 1.11.x | wings@1.11.x | :white_check_mark: |
| 0.7.x  | daemon@0.6.x | :x:                |


## Reporting a Vulnerability

Please reach out directly to any project team member on Discord when reporting a security vulnerability, or you can email `<EMAIL>`.

We make every effort to respond as soon as possible, although it may take a day or two for us to sync internally and determine the severity of the report and its impact. Please, _do not_ use a public facing channel or GitHub issues to report sensitive security issues.

As part of our process, we will create a security advisory for the affected versions and disclose it publicly, usually two to four weeks after a releasing a version that addresses it.
